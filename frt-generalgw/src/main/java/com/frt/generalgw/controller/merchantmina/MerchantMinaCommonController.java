/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.frt.generalgw.controller.merchantmina;

import com.frt.generalgw.client.usercore.merchantmina.MerchantMinaCommonClient;
import com.frt.generalgw.domain.param.merchantmina.common.CommonAddressCodeListQueryParam;
import com.frt.generalgw.domain.param.merchantmina.common.CommonUnityCategoryListQueryParam;
import com.frt.generalgw.domain.result.merchantmina.common.CommonAddressCodeListQueryResult;
import com.frt.generalgw.domain.result.merchantmina.common.CommonUnityCategoryListQueryResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商家版后台/MerchantMinaCommonController
 */
@RestController
@RequestMapping(value = "/merchant/mina/common")
public class MerchantMinaCommonController {

    @Autowired
    private MerchantMinaCommonClient commonClient;

    /**
     * 查询地址列表
     * @param param
     * @return
     */
    @PostMapping("/query/address-code-list")
    public CommonAddressCodeListQueryResult queryAddressCodeList(@RequestBody CommonAddressCodeListQueryParam param) {
        return commonClient.queryAddressCodeList(param);
    }

    /**
     * 查询类目列表
     *
     * @param param
     * @return
     */
    @PostMapping("/query/unity-category-list")
    public CommonUnityCategoryListQueryResult queryUnityCategoryList(@RequestBody CommonUnityCategoryListQueryParam param) {
        return commonClient.queryUnityCategoryList(param);
    }
}