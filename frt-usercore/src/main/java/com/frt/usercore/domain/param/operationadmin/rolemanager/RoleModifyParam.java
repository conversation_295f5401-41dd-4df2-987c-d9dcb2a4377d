package com.frt.usercore.domain.param.operationadmin.rolemanager;

import lombok.Data;

import java.util.List;

/**
 * 修改角色参数
 *
 * <AUTHOR>
 * @version RoleModifyParam.java, v 0.1 2025-08-28 15:00 tuyuwei
 */
@Data
public class RoleModifyParam {

    /**
     * 访问令牌
     */
    private String accessToken;

    /**
     * 角色ID
     */
    private String roleId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色类型 0-超级管理员 1-普通角色
     */
    private Integer roleType;

    /**
     * 终端类型 1-商户端 2-运营端
     */
    private Integer terminalType;

    /**
     * 租户 id
     */
    private String tenantId;

    /**
     * 权限列表
     */
    private List<String> permissionValueList;
}
