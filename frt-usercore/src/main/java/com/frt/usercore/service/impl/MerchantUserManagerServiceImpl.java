/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.frt.usercore.common.enums.business.AccountStatusEnum;
import com.frt.usercore.common.enums.business.EmployeeTypeEnum;
import com.frt.usercore.common.enums.business.PlatformEnum;
import com.frt.usercore.common.utils.ValidateUtil;
import com.frt.usercore.dao.entity.*;
import com.frt.usercore.dao.repository.*;
import com.frt.usercore.domain.entity.UserInfo;
import com.frt.usercore.domain.mapper.StoreManagerMapper;
import com.frt.usercore.domain.param.*;
import com.frt.usercore.domain.result.PageResult;
import com.frt.usercore.domain.result.UserDetailQueryResult;
import com.frt.usercore.domain.result.common.CommonResult;
import com.frt.usercore.service.MerchantUserManagerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version MerchantUserManagerServiceImpl.java, v 0.1 2025-08-27 17:58 zhangling
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MerchantUserManagerServiceImpl implements MerchantUserManagerService {

    private final MerchantUserDAO merchantUserDAO;

    private final MerchantDAO merchantDAO;

    private final AccountDAO accountDAO;

    private final TenantRoleDAO tenantRoleDAO;

    private final AccountBindRoleDAO accountBindRoleDAO;

    private final MerchantUserStoreDAO merchantUserStoreDAO;

    private final MerchantStoreInfoDAO merchantStoreInfoDAO;

    private final StoreManagerMapper storeManagerMapper;

    /**
     * 查询员工列表
     *
     * @param param 请求参数
     * @return 员工列表
     */
    @Override
    public PageResult<UserInfo> getUserList(PageParam<UserListQueryParam> param) {
        return null;
    }

    /**
     * 查询员工详情
     *
     * @param param 请求参数
     * @return 员工详情
     */
    @Override
    public UserDetailQueryResult getUserDetail(UserDetailQueryParam param) {
        log.info("MerchantUserManagerServiceImpl.getUserDetail >> 获取员工详情开始 >> param = {}", JSON.toJSONString(param));
        ValidateUtil.validate(param);
        final MerchantUserDO merchantUserDO = merchantUserDAO.getByMerchantIdAndUserId(param.getMerchantId(), param.getUserId());
        if (merchantUserDO == null) {

        }
        final AccountDO accountDO = accountDAO.getByUserId(param.getUserId());
        if (accountDO == null || AccountStatusEnum.CANCELLED.getCode().equals(accountDO.getAccountStatus())) {

        }
        final UserDetailQueryResult result = new UserDetailQueryResult();
        final List<MerchantUserStoreDO> storeDOList = merchantUserStoreDAO.findByUserIdAndMerchantId(param.getUserId(), param.getMerchantId());
        if (CollectionUtil.isNotEmpty(storeDOList)) {
            final List<String> storeIdList = storeDOList.stream().map(MerchantUserStoreDO::getStoreId).distinct().toList();
            final List<MerchantStoreInfoDO> storeInfoList = merchantStoreInfoDAO.findByStoreIdListAndMerchantId(storeIdList, param.getMerchantId());
            result.setStoreList(storeInfoList.stream().map(storeManagerMapper::coverMerchantStoreInfoDOToUserStoreResult).toList());
        }
        result.setUserId(accountDO.getUserId());
        result.setAccount(accountDO.getAccount());
        result.setName(accountDO.getName());
        result.setPhone(accountDO.getPhone());
        final AccountBindRoleDO accountBindRoleDO = accountBindRoleDAO.getByUserId(accountDO.getUserId());
        if (accountBindRoleDO != null) {
            result.setRoleId(accountBindRoleDO.getRoleId());
            final TenantRoleDO tenantRoleDO = tenantRoleDAO.getByRoleId(accountBindRoleDO.getRoleId());
            result.setRoleName(tenantRoleDO.getRoleName());
        }
        log.info("MerchantUserManagerServiceImpl.getUserDetail >> 获取员工详情结束 >> result = {}", JSON.toJSONString(merchantUserDO));
        return result;
    }

    /**
     * 添加员工
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @Override
    public CommonResult addUser(UserAddParam param) {
        // 校验账号是否已存在
        AccountDO existingAccount = accountDAO.getByAccount(param.getAccount());
        if (existingAccount != null) {
            return CommonResult.failed("员工账号已存在");
        }

        final TenantRoleDO roleDO = tenantRoleDAO.getByRoleId(param.getRoleId());
        if (roleDO == null) {
            return CommonResult.failed("角色不存在");
        }

        String userId = UUID.randomUUID().toString();
        // 创建账户信息
        AccountDO accountDO = new AccountDO();
        accountDO.setUserId(userId);
        accountDO.setAccount(param.getAccount());
        accountDO.setPassword(param.getPassword()); // 实际应用中密码需要加密
        accountDO.setSalt("");
        accountDO.setIsAdmin(0);
        accountDO.setPhone(param.getPhone());
        accountDO.setPlatformId(param.getMerchantId());
        accountDO.setPlatformType(PlatformEnum.MERCHANT.getCode());
        accountDO.setName(param.getName());
        accountDO.setAccountStatus(param.getStatus() != null ? ("Y".equals(param.getStatus()) ? 1 : 2) : 1);
        accountDAO.save(accountDO);

        //角色用户绑定关系
        AccountBindRoleDO accountBindRoleDO = new AccountBindRoleDO();
        accountBindRoleDO.setUserId(accountDO.getUserId());
        accountBindRoleDO.setRoleId(param.getRoleId());
        accountBindRoleDAO.save(accountBindRoleDO);

        // 创建商户用户关联信息
        MerchantUserDO merchantUserDO = new MerchantUserDO();
        merchantUserDO.setUserId(userId);
        merchantUserDO.setUsername(param.getAccount());
        // 设置租户和商户ID（需要根据实际情况获取）
        merchantUserDO.setTenantId(param.getMerchantId());
        merchantUserDO.setMerchantId(param.getMerchantId());
        merchantUserDO.setUserType(EmployeeTypeEnum.EMPLOYEE.name());
        merchantUserDAO.save(merchantUserDO);

        return CommonResult.success("添加员工成功");
    }

    /**
     * 更新员工
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @Override
    public CommonResult updateUser(UserUpdateParam param) {
        ValidateUtil.validate(param);
        // 校验角色是否存在（如果提供了角色ID）
        TenantRoleDO role = tenantRoleDAO.getByRoleId(param.getRoleId());
        if (role == null) {
            return CommonResult.failed("角色不存在");
        }

        // 更新商户用户信息
        MerchantUserDO merchantUserDO = merchantUserDAO.getByUserId(param.getUserId());
        if (merchantUserDO == null) {
            return CommonResult.failed("员工不存在");
        }

        final AccountDO accountDO = accountDAO.getByUserId(param.getUserId());
        if (accountDO == null) {
            return CommonResult.failed("员工不存在");
        }
        final AccountDO updateDO = new AccountDO();
        updateDO.setUserId(accountDO.getUserId());
        updateDO.setAccount(accountDO.getAccount());
        updateDO.setPassword(accountDO.getPassword());
        updateDO.setName(param.getName());
        updateDO.setPhone(param.getPhone());
        accountDAO.updateByUserId(updateDO);

        AccountBindRoleDO accountBindRoleDO = new AccountBindRoleDO();
        accountBindRoleDO.setUserId(accountDO.getUserId());
        accountBindRoleDO.setRoleId(role.getRoleId());
        accountBindRoleDAO.updateRoleIdByUserId(accountBindRoleDO);


        return CommonResult.success("更新员工成功");
    }

    /**
     * 删除员工
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @Override
    public CommonResult deleteUser(UserDeleteParam param) {
        ValidateUtil.validate(param);
        final MerchantUserDO merchantUserDO = merchantUserDAO.getByMerchantIdAndUserId(param.getMerchantId(), param.getUserId());
        if (merchantUserDO == null) {
            return CommonResult.failed("员工不存在");
        }
        final AccountDO accountDO = accountDAO.getByUserId(param.getUserId());
        if (accountDO == null || AccountStatusEnum.CANCELLED.getCode().equals(accountDO.getAccountStatus())) {
            return CommonResult.failed("员工不存在");
        }
        final AccountDO updateDO = new AccountDO();
        updateDO.setUserId(accountDO.getUserId());
        updateDO.setAccountStatus(AccountStatusEnum.CANCELLED.getCode());
        accountDAO.updateByUserId(updateDO);
        return CommonResult.success("删除员工成功");
    }

    /**
     * 禁用员工
     *
     * @param param 禁用员工参数
     * @return 操作结果
     */
    @Override
    public CommonResult disableAndEnableUser(UserDisableAndEnableParam param) {
        ValidateUtil.validate(param);
        final MerchantUserDO merchantUserDO = merchantUserDAO.getByMerchantIdAndUserId(param.getMerchantId(), param.getUserId());
        if (merchantUserDO == null) {
            return CommonResult.failed("员工不存在");
        }
        final AccountDO accountDO = accountDAO.getByUserId(param.getUserId());
        if (accountDO == null || AccountStatusEnum.CANCELLED.getCode().equals(accountDO.getAccountStatus())) {
            return CommonResult.failed("员工不存在");
        }
        final AccountDO updateDO = new AccountDO();
        updateDO.setUserId(accountDO.getUserId());
        updateDO.setAccountStatus(AccountStatusEnum.NORMAL.getCode().equals(accountDO.getAccountStatus()) ? AccountStatusEnum.DISABLED.getCode() : AccountStatusEnum.NORMAL.getCode());
        accountDAO.updateByUserId(updateDO);
        return CommonResult.success("更新员工成功");
    }
}