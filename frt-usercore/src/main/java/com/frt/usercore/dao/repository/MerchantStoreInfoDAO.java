package com.frt.usercore.dao.repository;

import com.frt.usercore.dao.entity.MerchantStoreInfoDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 门店信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-27
 */
public interface MerchantStoreInfoDAO extends IService<MerchantStoreInfoDO> {

    /**
     * 根据门店id和商户id查询门店信息
     * @param storeIdList
     * @param merchantId
     * @return
     */
    List<MerchantStoreInfoDO> findByStoreIdListAndMerchantId(List<String> storeIdList, String merchantId);

}
