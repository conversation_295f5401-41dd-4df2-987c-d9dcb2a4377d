package com.frt.usercore.dao.repository;

import com.frt.usercore.dao.entity.AccountBindRoleDO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 账号角色关系表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
public interface AccountBindRoleDAO extends IService<AccountBindRoleDO> {

    void updateRoleIdByUserId(AccountBindRoleDO accountBindRoleDO);

    /**
     * 根据用户id查询
     * @param userId
     * @return
     */
    AccountBindRoleDO getByUserId(String userId);

}
