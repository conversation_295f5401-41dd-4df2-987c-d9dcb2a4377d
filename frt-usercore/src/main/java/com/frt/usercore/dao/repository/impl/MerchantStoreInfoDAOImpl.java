package com.frt.usercore.dao.repository.impl;

import com.frt.usercore.dao.entity.MerchantStoreInfoDO;
import com.frt.usercore.dao.entity.MerchantUserDO;
import com.frt.usercore.dao.mapper.MerchantStoreInfoMapper;
import com.frt.usercore.dao.repository.MerchantStoreInfoDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 门店信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-27
 */
@Service
public class MerchantStoreInfoDAOImpl extends ServiceImpl<MerchantStoreInfoMapper, MerchantStoreInfoDO> implements MerchantStoreInfoDAO {

    @Override
    public List<MerchantStoreInfoDO> findByStoreIdListAndMerchantId(List<String> storeIdList, String merchantId) {
       return this.lambdaQuery()
                .in(MerchantStoreInfoDO::getStoreId, storeIdList)
                .eq(MerchantStoreInfoDO::getMerchantId, merchantId)
                .eq(MerchantStoreInfoDO::getIsDel, 0)
               .list();
    }
}
